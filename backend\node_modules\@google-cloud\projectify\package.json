{"name": "@google-cloud/projectify", "version": "4.0.0", "description": "A simple utility for replacing the projectid token in objects.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "googleapis/nodejs-projectify", "scripts": {"test": "c8 mocha build/test", "lint": "gts check", "clean": "gts clean", "compile": "tsc -p .", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "docs": "compodoc src/", "presystem-test": "npm run compile", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "system-test": "mocha build/system-test", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean"}, "keywords": [], "files": ["build/src", "!build/src/**/*.map"], "author": "Google Inc.", "license": "Apache-2.0", "devDependencies": {"@compodoc/compodoc": "^1.1.11", "@types/mocha": "^9.0.0", "@types/node": "^20.4.9", "c8": "^8.0.1", "codecov": "^3.6.5", "gts": "^5.0.0", "linkinator": "^4.0.0", "mocha": "^9.2.2", "typescript": "^5.1.6"}, "engines": {"node": ">=14.0.0"}}