{"name": "@google-cloud/promisify", "version": "4.1.0", "description": "A simple utility for promisifying functions and classes.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "googleapis/nodejs-promisify", "scripts": {"test": "c8 mocha build/test", "lint": "gts check", "compile": "tsc -p .", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "docs": "jsdoc -c .jsdoc.js", "presystem-test": "npm run compile", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "system-test": "mocha build/system-test", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "keywords": [], "files": ["build/src", "!build/src/**/*.map"], "author": "Google Inc.", "license": "Apache-2.0", "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^22.13.9", "@types/sinon": "^17.0.4", "c8": "^10.1.3", "chai": "^5.2.0", "codecov": "^3.8.3", "gts": "^6.0.2", "hard-rejection": "^2.1.0", "jsdoc": "^4.0.4", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "mocha": "^11.1.0", "sinon": "^19.0.2", "typescript": "^5.8.2"}, "engines": {"node": ">=18"}}