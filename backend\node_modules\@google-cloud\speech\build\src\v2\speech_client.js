"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpeechClient = void 0;
const stream_1 = require("stream");
const jsonProtos = require("../../protos/protos.json");
/**
 * Client JSON configuration object, loaded from
 * `src/v2/speech_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./speech_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Enables speech transcription and resource management.
 * @class
 * @memberof v2
 */
class SpeechClient {
    /**
     * Construct an instance of SpeechClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new SpeechClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        var _a, _b, _c, _d, _e;
        this._terminated = false;
        this.descriptors = {
            page: {},
            stream: {},
            longrunning: {},
            batching: {},
        };
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if ((opts === null || opts === void 0 ? void 0 : opts.universe_domain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universeDomain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== (opts === null || opts === void 0 ? void 0 : opts.universeDomain)) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            (_c = (_b = (_a = opts === null || opts === void 0 ? void 0 : opts.universeDomain) !== null && _a !== void 0 ? _a : opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== null && _b !== void 0 ? _b : universeDomainEnvVar) !== null && _c !== void 0 ? _c : 'googleapis.com';
        this._servicePath = 'speech.' + this._universeDomain;
        const servicePath = (opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint) || this._servicePath;
        this._providedCustomServicePath = !!((opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint));
        const port = (opts === null || opts === void 0 ? void 0 : opts.port) || staticMembers.port;
        const clientConfig = (_d = opts === null || opts === void 0 ? void 0 : opts.clientConfig) !== null && _d !== void 0 ? _d : {};
        const fallback = (_e = opts === null || opts === void 0 ? void 0 : opts.fallback) !== null && _e !== void 0 ? _e : (typeof window !== 'undefined' && typeof (window === null || window === void 0 ? void 0 : window.fetch) === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            configPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/config'),
            cryptoKeyPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}'),
            cryptoKeyVersionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}/cryptoKeyVersions/{crypto_key_version}'),
            customClassPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/customClasses/{custom_class}'),
            locationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}'),
            phraseSetPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/phraseSets/{phrase_set}'),
            projectPathTemplate: new this._gaxModule.PathTemplate('projects/{project}'),
            recognizerPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/recognizers/{recognizer}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listRecognizers: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'recognizers'),
            listCustomClasses: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'customClasses'),
            listPhraseSets: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'phraseSets'),
        };
        // Some of the methods on this service provide streaming responses.
        // Provide descriptors for these.
        this.descriptors.stream = {
            streamingRecognize: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.BIDI_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
        };
        const protoFilesRoot = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined,
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [
                {
                    selector: 'google.cloud.location.Locations.GetLocation',
                    get: '/v2/{name=projects/*/locations/*}',
                },
                {
                    selector: 'google.cloud.location.Locations.ListLocations',
                    get: '/v2/{name=projects/*}/locations',
                },
                {
                    selector: 'google.longrunning.Operations.CancelOperation',
                    post: '/v2/{name=projects/*/locations/*/operations/*}:cancel',
                    body: '*',
                },
                {
                    selector: 'google.longrunning.Operations.DeleteOperation',
                    delete: '/v2/{name=projects/*/locations/*/operations/*}',
                },
                {
                    selector: 'google.longrunning.Operations.GetOperation',
                    get: '/v2/{name=projects/*/locations/*/operations/*}',
                },
                {
                    selector: 'google.longrunning.Operations.ListOperations',
                    get: '/v2/{name=projects/*/locations/*}/operations',
                },
            ];
        }
        this.operationsClient = this._gaxModule
            .lro(lroOptions)
            .operationsClient(opts);
        const createRecognizerResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.Recognizer');
        const createRecognizerMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const updateRecognizerResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.Recognizer');
        const updateRecognizerMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const deleteRecognizerResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.Recognizer');
        const deleteRecognizerMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const undeleteRecognizerResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.Recognizer');
        const undeleteRecognizerMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const batchRecognizeResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.BatchRecognizeResponse');
        const batchRecognizeMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const createCustomClassResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.CustomClass');
        const createCustomClassMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const updateCustomClassResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.CustomClass');
        const updateCustomClassMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const deleteCustomClassResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.CustomClass');
        const deleteCustomClassMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const undeleteCustomClassResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.CustomClass');
        const undeleteCustomClassMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const createPhraseSetResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.PhraseSet');
        const createPhraseSetMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const updatePhraseSetResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.PhraseSet');
        const updatePhraseSetMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const deletePhraseSetResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.PhraseSet');
        const deletePhraseSetMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        const undeletePhraseSetResponse = protoFilesRoot.lookup('.google.cloud.speech.v2.PhraseSet');
        const undeletePhraseSetMetadata = protoFilesRoot.lookup('.google.cloud.speech.v2.OperationMetadata');
        this.descriptors.longrunning = {
            createRecognizer: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createRecognizerResponse.decode.bind(createRecognizerResponse), createRecognizerMetadata.decode.bind(createRecognizerMetadata)),
            updateRecognizer: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updateRecognizerResponse.decode.bind(updateRecognizerResponse), updateRecognizerMetadata.decode.bind(updateRecognizerMetadata)),
            deleteRecognizer: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteRecognizerResponse.decode.bind(deleteRecognizerResponse), deleteRecognizerMetadata.decode.bind(deleteRecognizerMetadata)),
            undeleteRecognizer: new this._gaxModule.LongrunningDescriptor(this.operationsClient, undeleteRecognizerResponse.decode.bind(undeleteRecognizerResponse), undeleteRecognizerMetadata.decode.bind(undeleteRecognizerMetadata)),
            batchRecognize: new this._gaxModule.LongrunningDescriptor(this.operationsClient, batchRecognizeResponse.decode.bind(batchRecognizeResponse), batchRecognizeMetadata.decode.bind(batchRecognizeMetadata)),
            createCustomClass: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createCustomClassResponse.decode.bind(createCustomClassResponse), createCustomClassMetadata.decode.bind(createCustomClassMetadata)),
            updateCustomClass: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updateCustomClassResponse.decode.bind(updateCustomClassResponse), updateCustomClassMetadata.decode.bind(updateCustomClassMetadata)),
            deleteCustomClass: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteCustomClassResponse.decode.bind(deleteCustomClassResponse), deleteCustomClassMetadata.decode.bind(deleteCustomClassMetadata)),
            undeleteCustomClass: new this._gaxModule.LongrunningDescriptor(this.operationsClient, undeleteCustomClassResponse.decode.bind(undeleteCustomClassResponse), undeleteCustomClassMetadata.decode.bind(undeleteCustomClassMetadata)),
            createPhraseSet: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createPhraseSetResponse.decode.bind(createPhraseSetResponse), createPhraseSetMetadata.decode.bind(createPhraseSetMetadata)),
            updatePhraseSet: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updatePhraseSetResponse.decode.bind(updatePhraseSetResponse), updatePhraseSetMetadata.decode.bind(updatePhraseSetMetadata)),
            deletePhraseSet: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deletePhraseSetResponse.decode.bind(deletePhraseSetResponse), deletePhraseSetMetadata.decode.bind(deletePhraseSetMetadata)),
            undeletePhraseSet: new this._gaxModule.LongrunningDescriptor(this.operationsClient, undeletePhraseSetResponse.decode.bind(undeletePhraseSetResponse), undeletePhraseSetMetadata.decode.bind(undeletePhraseSetMetadata)),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.speech.v2.Speech', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.speechStub) {
            return this.speechStub;
        }
        // Put together the "service stub" for
        // google.cloud.speech.v2.Speech.
        this.speechStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.cloud.speech.v2.Speech')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.cloud.speech.v2.Speech, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const speechStubMethods = [
            'createRecognizer',
            'listRecognizers',
            'getRecognizer',
            'updateRecognizer',
            'deleteRecognizer',
            'undeleteRecognizer',
            'recognize',
            'streamingRecognize',
            'batchRecognize',
            'getConfig',
            'updateConfig',
            'createCustomClass',
            'listCustomClasses',
            'getCustomClass',
            'updateCustomClass',
            'deleteCustomClass',
            'undeleteCustomClass',
            'createPhraseSet',
            'listPhraseSets',
            'getPhraseSet',
            'updatePhraseSet',
            'deletePhraseSet',
            'undeletePhraseSet',
        ];
        for (const methodName of speechStubMethods) {
            const callPromise = this.speechStub.then(stub => (...args) => {
                if (this._terminated) {
                    if (methodName in this.descriptors.stream) {
                        const stream = new stream_1.PassThrough({ objectMode: true });
                        setImmediate(() => {
                            stream.emit('error', new this._gaxModule.GoogleError('The client has already been closed.'));
                        });
                        return stream;
                    }
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.stream[methodName] ||
                this.descriptors.longrunning[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.speechStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'speech.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'speech.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return ['https://www.googleapis.com/auth/cloud-platform'];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    getRecognizer(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getRecognizer(request, options, callback);
    }
    recognize(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                recognizer: (_a = request.recognizer) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.recognize(request, options, callback);
    }
    getConfig(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getConfig(request, options, callback);
    }
    updateConfig(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'config.name': (_a = request.config.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateConfig(request, options, callback);
    }
    getCustomClass(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getCustomClass(request, options, callback);
    }
    getPhraseSet(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getPhraseSet(request, options, callback);
    }
    /**
     * Performs bidirectional streaming speech recognition: receive results while
     * sending audio. This method is only available via the gRPC API (not REST).
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.cloud.speech.v2.StreamingRecognizeRequest|StreamingRecognizeRequest} for write() method, and
     *   will emit objects representing {@link protos.google.cloud.speech.v2.StreamingRecognizeResponse|StreamingRecognizeResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.streaming_recognize.js</caption>
     * region_tag:speech_v2_generated_Speech_StreamingRecognize_async
     */
    _streamingRecognize(options) {
        this.initialize();
        return this.innerApiCalls.streamingRecognize(null, options);
    }
    createRecognizer(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createRecognizer(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `createRecognizer()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.create_recognizer.js</caption>
     * region_tag:speech_v2_generated_Speech_CreateRecognizer_async
     */
    async checkCreateRecognizerProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createRecognizer, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updateRecognizer(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'recognizer.name': (_a = request.recognizer.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateRecognizer(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `updateRecognizer()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.update_recognizer.js</caption>
     * region_tag:speech_v2_generated_Speech_UpdateRecognizer_async
     */
    async checkUpdateRecognizerProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updateRecognizer, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteRecognizer(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteRecognizer(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `deleteRecognizer()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.delete_recognizer.js</caption>
     * region_tag:speech_v2_generated_Speech_DeleteRecognizer_async
     */
    async checkDeleteRecognizerProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteRecognizer, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    undeleteRecognizer(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.undeleteRecognizer(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `undeleteRecognizer()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.undelete_recognizer.js</caption>
     * region_tag:speech_v2_generated_Speech_UndeleteRecognizer_async
     */
    async checkUndeleteRecognizerProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.undeleteRecognizer, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    batchRecognize(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                recognizer: (_a = request.recognizer) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.batchRecognize(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `batchRecognize()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.batch_recognize.js</caption>
     * region_tag:speech_v2_generated_Speech_BatchRecognize_async
     */
    async checkBatchRecognizeProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.batchRecognize, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createCustomClass(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createCustomClass(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `createCustomClass()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.create_custom_class.js</caption>
     * region_tag:speech_v2_generated_Speech_CreateCustomClass_async
     */
    async checkCreateCustomClassProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createCustomClass, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updateCustomClass(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'custom_class.name': (_a = request.customClass.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateCustomClass(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `updateCustomClass()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.update_custom_class.js</caption>
     * region_tag:speech_v2_generated_Speech_UpdateCustomClass_async
     */
    async checkUpdateCustomClassProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updateCustomClass, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteCustomClass(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteCustomClass(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `deleteCustomClass()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.delete_custom_class.js</caption>
     * region_tag:speech_v2_generated_Speech_DeleteCustomClass_async
     */
    async checkDeleteCustomClassProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteCustomClass, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    undeleteCustomClass(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.undeleteCustomClass(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `undeleteCustomClass()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.undelete_custom_class.js</caption>
     * region_tag:speech_v2_generated_Speech_UndeleteCustomClass_async
     */
    async checkUndeleteCustomClassProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.undeleteCustomClass, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createPhraseSet(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createPhraseSet(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `createPhraseSet()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.create_phrase_set.js</caption>
     * region_tag:speech_v2_generated_Speech_CreatePhraseSet_async
     */
    async checkCreatePhraseSetProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createPhraseSet, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updatePhraseSet(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'phrase_set.name': (_a = request.phraseSet.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updatePhraseSet(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `updatePhraseSet()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.update_phrase_set.js</caption>
     * region_tag:speech_v2_generated_Speech_UpdatePhraseSet_async
     */
    async checkUpdatePhraseSetProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updatePhraseSet, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deletePhraseSet(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deletePhraseSet(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `deletePhraseSet()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.delete_phrase_set.js</caption>
     * region_tag:speech_v2_generated_Speech_DeletePhraseSet_async
     */
    async checkDeletePhraseSetProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deletePhraseSet, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    undeletePhraseSet(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.undeletePhraseSet(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `undeletePhraseSet()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.undelete_phrase_set.js</caption>
     * region_tag:speech_v2_generated_Speech_UndeletePhraseSet_async
     */
    async checkUndeletePhraseSetProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.undeletePhraseSet, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    listRecognizers(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listRecognizers(request, options, callback);
    }
    /**
     * Equivalent to `listRecognizers`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project and location of Recognizers to list. The expected
     *   format is `projects/{project}/locations/{location}`.
     * @param {number} request.pageSize
     *   The maximum number of Recognizers to return. The service may return fewer
     *   than this value. If unspecified, at most 5 Recognizers will be returned.
     *   The maximum value is 100; values above 100 will be coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.speech.v2.Speech.ListRecognizers|ListRecognizers} call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.speech.v2.Speech.ListRecognizers|ListRecognizers} must match
     *   the call that provided the page token.
     * @param {boolean} request.showDeleted
     *   Whether, or not, to show resources that have been deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.speech.v2.Recognizer|Recognizer} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listRecognizersAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listRecognizersStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listRecognizers'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listRecognizers.createStream(this.innerApiCalls.listRecognizers, request, callSettings);
    }
    /**
     * Equivalent to `listRecognizers`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project and location of Recognizers to list. The expected
     *   format is `projects/{project}/locations/{location}`.
     * @param {number} request.pageSize
     *   The maximum number of Recognizers to return. The service may return fewer
     *   than this value. If unspecified, at most 5 Recognizers will be returned.
     *   The maximum value is 100; values above 100 will be coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.speech.v2.Speech.ListRecognizers|ListRecognizers} call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.speech.v2.Speech.ListRecognizers|ListRecognizers} must match
     *   the call that provided the page token.
     * @param {boolean} request.showDeleted
     *   Whether, or not, to show resources that have been deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.speech.v2.Recognizer|Recognizer}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.list_recognizers.js</caption>
     * region_tag:speech_v2_generated_Speech_ListRecognizers_async
     */
    listRecognizersAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listRecognizers'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listRecognizers.asyncIterate(this.innerApiCalls['listRecognizers'], request, callSettings);
    }
    listCustomClasses(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listCustomClasses(request, options, callback);
    }
    /**
     * Equivalent to `listCustomClasses`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project and location of CustomClass resources to list. The
     *   expected format is `projects/{project}/locations/{location}`.
     * @param {number} request.pageSize
     *   Number of results per requests. A valid page_size ranges from 0 to 100
     *   inclusive. If the page_size is zero or unspecified, a page size of 5 will
     *   be chosen. If the page size exceeds 100, it will be coerced down to 100.
     *   Note that a call might return fewer results than the requested page size.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.speech.v2.Speech.ListCustomClasses|ListCustomClasses} call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.speech.v2.Speech.ListCustomClasses|ListCustomClasses} must
     *   match the call that provided the page token.
     * @param {boolean} request.showDeleted
     *   Whether, or not, to show resources that have been deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.speech.v2.CustomClass|CustomClass} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listCustomClassesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listCustomClassesStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listCustomClasses'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listCustomClasses.createStream(this.innerApiCalls.listCustomClasses, request, callSettings);
    }
    /**
     * Equivalent to `listCustomClasses`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project and location of CustomClass resources to list. The
     *   expected format is `projects/{project}/locations/{location}`.
     * @param {number} request.pageSize
     *   Number of results per requests. A valid page_size ranges from 0 to 100
     *   inclusive. If the page_size is zero or unspecified, a page size of 5 will
     *   be chosen. If the page size exceeds 100, it will be coerced down to 100.
     *   Note that a call might return fewer results than the requested page size.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.speech.v2.Speech.ListCustomClasses|ListCustomClasses} call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.speech.v2.Speech.ListCustomClasses|ListCustomClasses} must
     *   match the call that provided the page token.
     * @param {boolean} request.showDeleted
     *   Whether, or not, to show resources that have been deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.speech.v2.CustomClass|CustomClass}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.list_custom_classes.js</caption>
     * region_tag:speech_v2_generated_Speech_ListCustomClasses_async
     */
    listCustomClassesAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listCustomClasses'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listCustomClasses.asyncIterate(this.innerApiCalls['listCustomClasses'], request, callSettings);
    }
    listPhraseSets(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listPhraseSets(request, options, callback);
    }
    /**
     * Equivalent to `listPhraseSets`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project and location of PhraseSet resources to list. The
     *   expected format is `projects/{project}/locations/{location}`.
     * @param {number} request.pageSize
     *   The maximum number of PhraseSets to return. The service may return fewer
     *   than this value. If unspecified, at most 5 PhraseSets will be returned.
     *   The maximum value is 100; values above 100 will be coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.speech.v2.Speech.ListPhraseSets|ListPhraseSets} call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.speech.v2.Speech.ListPhraseSets|ListPhraseSets} must match
     *   the call that provided the page token.
     * @param {boolean} request.showDeleted
     *   Whether, or not, to show resources that have been deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.speech.v2.PhraseSet|PhraseSet} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listPhraseSetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listPhraseSetsStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listPhraseSets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listPhraseSets.createStream(this.innerApiCalls.listPhraseSets, request, callSettings);
    }
    /**
     * Equivalent to `listPhraseSets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project and location of PhraseSet resources to list. The
     *   expected format is `projects/{project}/locations/{location}`.
     * @param {number} request.pageSize
     *   The maximum number of PhraseSets to return. The service may return fewer
     *   than this value. If unspecified, at most 5 PhraseSets will be returned.
     *   The maximum value is 100; values above 100 will be coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.speech.v2.Speech.ListPhraseSets|ListPhraseSets} call.
     *   Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.speech.v2.Speech.ListPhraseSets|ListPhraseSets} must match
     *   the call that provided the page token.
     * @param {boolean} request.showDeleted
     *   Whether, or not, to show resources that have been deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.speech.v2.PhraseSet|PhraseSet}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v2/speech.list_phrase_sets.js</caption>
     * region_tag:speech_v2_generated_Speech_ListPhraseSets_async
     */
    listPhraseSetsAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listPhraseSets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listPhraseSets.asyncIterate(this.innerApiCalls['listPhraseSets'], request, callSettings);
    }
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.getOperation(request, options, callback);
    }
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request, options) {
        var _a;
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.listOperationsAsync(request, options);
    }
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.cancelOperation(request, options, callback);
    }
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.deleteOperation(request, options, callback);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified config resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    configPath(project, location) {
        return this.pathTemplates.configPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from Config resource.
     *
     * @param {string} configName
     *   A fully-qualified path representing Config resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromConfigName(configName) {
        return this.pathTemplates.configPathTemplate.match(configName).project;
    }
    /**
     * Parse the location from Config resource.
     *
     * @param {string} configName
     *   A fully-qualified path representing Config resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromConfigName(configName) {
        return this.pathTemplates.configPathTemplate.match(configName).location;
    }
    /**
     * Return a fully-qualified cryptoKey resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} key_ring
     * @param {string} crypto_key
     * @returns {string} Resource name string.
     */
    cryptoKeyPath(project, location, keyRing, cryptoKey) {
        return this.pathTemplates.cryptoKeyPathTemplate.render({
            project: project,
            location: location,
            key_ring: keyRing,
            crypto_key: cryptoKey,
        });
    }
    /**
     * Parse the project from CryptoKey resource.
     *
     * @param {string} cryptoKeyName
     *   A fully-qualified path representing CryptoKey resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCryptoKeyName(cryptoKeyName) {
        return this.pathTemplates.cryptoKeyPathTemplate.match(cryptoKeyName)
            .project;
    }
    /**
     * Parse the location from CryptoKey resource.
     *
     * @param {string} cryptoKeyName
     *   A fully-qualified path representing CryptoKey resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCryptoKeyName(cryptoKeyName) {
        return this.pathTemplates.cryptoKeyPathTemplate.match(cryptoKeyName)
            .location;
    }
    /**
     * Parse the key_ring from CryptoKey resource.
     *
     * @param {string} cryptoKeyName
     *   A fully-qualified path representing CryptoKey resource.
     * @returns {string} A string representing the key_ring.
     */
    matchKeyRingFromCryptoKeyName(cryptoKeyName) {
        return this.pathTemplates.cryptoKeyPathTemplate.match(cryptoKeyName)
            .key_ring;
    }
    /**
     * Parse the crypto_key from CryptoKey resource.
     *
     * @param {string} cryptoKeyName
     *   A fully-qualified path representing CryptoKey resource.
     * @returns {string} A string representing the crypto_key.
     */
    matchCryptoKeyFromCryptoKeyName(cryptoKeyName) {
        return this.pathTemplates.cryptoKeyPathTemplate.match(cryptoKeyName)
            .crypto_key;
    }
    /**
     * Return a fully-qualified cryptoKeyVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} key_ring
     * @param {string} crypto_key
     * @param {string} crypto_key_version
     * @returns {string} Resource name string.
     */
    cryptoKeyVersionPath(project, location, keyRing, cryptoKey, cryptoKeyVersion) {
        return this.pathTemplates.cryptoKeyVersionPathTemplate.render({
            project: project,
            location: location,
            key_ring: keyRing,
            crypto_key: cryptoKey,
            crypto_key_version: cryptoKeyVersion,
        });
    }
    /**
     * Parse the project from CryptoKeyVersion resource.
     *
     * @param {string} cryptoKeyVersionName
     *   A fully-qualified path representing CryptoKeyVersion resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCryptoKeyVersionName(cryptoKeyVersionName) {
        return this.pathTemplates.cryptoKeyVersionPathTemplate.match(cryptoKeyVersionName).project;
    }
    /**
     * Parse the location from CryptoKeyVersion resource.
     *
     * @param {string} cryptoKeyVersionName
     *   A fully-qualified path representing CryptoKeyVersion resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCryptoKeyVersionName(cryptoKeyVersionName) {
        return this.pathTemplates.cryptoKeyVersionPathTemplate.match(cryptoKeyVersionName).location;
    }
    /**
     * Parse the key_ring from CryptoKeyVersion resource.
     *
     * @param {string} cryptoKeyVersionName
     *   A fully-qualified path representing CryptoKeyVersion resource.
     * @returns {string} A string representing the key_ring.
     */
    matchKeyRingFromCryptoKeyVersionName(cryptoKeyVersionName) {
        return this.pathTemplates.cryptoKeyVersionPathTemplate.match(cryptoKeyVersionName).key_ring;
    }
    /**
     * Parse the crypto_key from CryptoKeyVersion resource.
     *
     * @param {string} cryptoKeyVersionName
     *   A fully-qualified path representing CryptoKeyVersion resource.
     * @returns {string} A string representing the crypto_key.
     */
    matchCryptoKeyFromCryptoKeyVersionName(cryptoKeyVersionName) {
        return this.pathTemplates.cryptoKeyVersionPathTemplate.match(cryptoKeyVersionName).crypto_key;
    }
    /**
     * Parse the crypto_key_version from CryptoKeyVersion resource.
     *
     * @param {string} cryptoKeyVersionName
     *   A fully-qualified path representing CryptoKeyVersion resource.
     * @returns {string} A string representing the crypto_key_version.
     */
    matchCryptoKeyVersionFromCryptoKeyVersionName(cryptoKeyVersionName) {
        return this.pathTemplates.cryptoKeyVersionPathTemplate.match(cryptoKeyVersionName).crypto_key_version;
    }
    /**
     * Return a fully-qualified customClass resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} custom_class
     * @returns {string} Resource name string.
     */
    customClassPath(project, location, customClass) {
        return this.pathTemplates.customClassPathTemplate.render({
            project: project,
            location: location,
            custom_class: customClass,
        });
    }
    /**
     * Parse the project from CustomClass resource.
     *
     * @param {string} customClassName
     *   A fully-qualified path representing CustomClass resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCustomClassName(customClassName) {
        return this.pathTemplates.customClassPathTemplate.match(customClassName)
            .project;
    }
    /**
     * Parse the location from CustomClass resource.
     *
     * @param {string} customClassName
     *   A fully-qualified path representing CustomClass resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCustomClassName(customClassName) {
        return this.pathTemplates.customClassPathTemplate.match(customClassName)
            .location;
    }
    /**
     * Parse the custom_class from CustomClass resource.
     *
     * @param {string} customClassName
     *   A fully-qualified path representing CustomClass resource.
     * @returns {string} A string representing the custom_class.
     */
    matchCustomClassFromCustomClassName(customClassName) {
        return this.pathTemplates.customClassPathTemplate.match(customClassName)
            .custom_class;
    }
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project, location) {
        return this.pathTemplates.locationPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).project;
    }
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).location;
    }
    /**
     * Return a fully-qualified phraseSet resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} phrase_set
     * @returns {string} Resource name string.
     */
    phraseSetPath(project, location, phraseSet) {
        return this.pathTemplates.phraseSetPathTemplate.render({
            project: project,
            location: location,
            phrase_set: phraseSet,
        });
    }
    /**
     * Parse the project from PhraseSet resource.
     *
     * @param {string} phraseSetName
     *   A fully-qualified path representing PhraseSet resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPhraseSetName(phraseSetName) {
        return this.pathTemplates.phraseSetPathTemplate.match(phraseSetName)
            .project;
    }
    /**
     * Parse the location from PhraseSet resource.
     *
     * @param {string} phraseSetName
     *   A fully-qualified path representing PhraseSet resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPhraseSetName(phraseSetName) {
        return this.pathTemplates.phraseSetPathTemplate.match(phraseSetName)
            .location;
    }
    /**
     * Parse the phrase_set from PhraseSet resource.
     *
     * @param {string} phraseSetName
     *   A fully-qualified path representing PhraseSet resource.
     * @returns {string} A string representing the phrase_set.
     */
    matchPhraseSetFromPhraseSetName(phraseSetName) {
        return this.pathTemplates.phraseSetPathTemplate.match(phraseSetName)
            .phrase_set;
    }
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectPath(project) {
        return this.pathTemplates.projectPathTemplate.render({
            project: project,
        });
    }
    /**
     * Parse the project from Project resource.
     *
     * @param {string} projectName
     *   A fully-qualified path representing Project resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectName(projectName) {
        return this.pathTemplates.projectPathTemplate.match(projectName).project;
    }
    /**
     * Return a fully-qualified recognizer resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} recognizer
     * @returns {string} Resource name string.
     */
    recognizerPath(project, location, recognizer) {
        return this.pathTemplates.recognizerPathTemplate.render({
            project: project,
            location: location,
            recognizer: recognizer,
        });
    }
    /**
     * Parse the project from Recognizer resource.
     *
     * @param {string} recognizerName
     *   A fully-qualified path representing Recognizer resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRecognizerName(recognizerName) {
        return this.pathTemplates.recognizerPathTemplate.match(recognizerName)
            .project;
    }
    /**
     * Parse the location from Recognizer resource.
     *
     * @param {string} recognizerName
     *   A fully-qualified path representing Recognizer resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRecognizerName(recognizerName) {
        return this.pathTemplates.recognizerPathTemplate.match(recognizerName)
            .location;
    }
    /**
     * Parse the recognizer from Recognizer resource.
     *
     * @param {string} recognizerName
     *   A fully-qualified path representing Recognizer resource.
     * @returns {string} A string representing the recognizer.
     */
    matchRecognizerFromRecognizerName(recognizerName) {
        return this.pathTemplates.recognizerPathTemplate.match(recognizerName)
            .recognizer;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.speechStub && !this._terminated) {
            return this.speechStub.then(stub => {
                this._terminated = true;
                stub.close();
                this.locationsClient.close();
                this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.SpeechClient = SpeechClient;
//# sourceMappingURL=speech_client.js.map