@protobufjs/base64
==================
[![npm](https://img.shields.io/npm/v/@protobufjs/base64.svg)](https://www.npmjs.com/package/@protobufjs/base64)

A minimal base64 implementation for number arrays.

API
---

* **base64.length(string: `string`): `number`**<br />
  Calculates the byte length of a base64 encoded string.

* **base64.encode(buffer: `Uint8Array`, start: `number`, end: `number`): `string`**<br />
  Encodes a buffer to a base64 encoded string.

* **base64.decode(string: `string`, buffer: `Uint8Array`, offset: `number`): `number`**<br />
  Decodes a base64 encoded string to a buffer.

**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)
