{"name": "arrify", "version": "2.0.1", "description": "Convert a value to an array", "license": "MIT", "repository": "sindresorhus/arrify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["array", "arrify", "arrayify", "convert", "value", "ensure"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}