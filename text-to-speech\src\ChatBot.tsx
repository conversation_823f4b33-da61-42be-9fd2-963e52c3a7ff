import React, { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>, User, <PERSON><PERSON>2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const DID_API_URL = "https://api.d-id.com/talks";

const ChatBot: React.FC = () => {
  const [text, setText] = useState<string>("");
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [messages, setMessages] = useState<
    Array<{ id: number; text: string; sender: "user" | "bot"; timestamp: Date }>
  >([]);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isTranscribing, setIsTranscribing] = useState<boolean>(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const createTalk = async () => {
    if (text.trim() === "") return;

    // Add user message
    const userMessage = {
      id: Date.now(),
      text: text.trim(),
      sender: "user" as const,
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, userMessage]);

    setLoading(true);
    setVideoUrl(null);
    setText(""); // Clear input immediately

    const payload = {
      script: {
        type: "text",
        input: userMessage.text,
        // You can add voice_id, provider, style, etc.
      },
      // source_url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSpUyY77jqPWaZyIzr1aP-nErZUNeuyHcgoyQ&s', // Optional
    };

    try {
      const response = await fetch(DID_API_URL, {
        method: "POST",
        headers: {
          Authorization: `Basic ${btoa(`${import.meta.env.VITE_DID_API_KEY}`)}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to create talk: ${response.statusText}`);
      }

      const data = await response.json();
      const talkId = data.id;

      let videoUrlResponse: string | null = null;

      while (!videoUrlResponse) {
        const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
          headers: {
            Authorization: `Basic ${btoa(
              `${import.meta.env.VITE_DID_API_KEY}`
            )}`,
            "Content-Type": "application/json",
          },
        });

        const pollData = await pollResp.json();

        if (pollData.status === "done" && pollData.result_url) {
          videoUrlResponse = pollData.result_url;
        } else {
          await new Promise((res) => setTimeout(res, 3000));
        }
      }

      setVideoUrl(videoUrlResponse);

      // Add bot response message
      const botMessage = {
        id: Date.now() + 1,
        text: userMessage.text,
        sender: "bot" as const,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, botMessage]);
    } catch (err: any) {
      console.error(err);
      alert("Error generating video: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    createTalk();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      createTalk();
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await transcribeAudio(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Error accessing microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    setIsTranscribing(true);
    try {
      const reader = new FileReader();
      reader.onloadend = async () => {
        const base64Audio = (reader.result as string).split(',')[1];

        const response = await fetch('http://localhost:5000/speech-to-text', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ audioBase64: base64Audio }),
        });

        if (!response.ok) {
          throw new Error('Failed to transcribe audio');
        }

        const data = await response.json();
        if (data.transcription) {
          setText(data.transcription);
        }
      };
      reader.readAsDataURL(audioBlob);
    } catch (error) {
      console.error('Error transcribing audio:', error);
      alert('Error transcribing audio. Please try again.');
    } finally {
      setIsTranscribing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-2 sm:p-4">
      <div className="w-full max-w-7xl h-screen sm:h-[95vh] lg:h-[85vh] bg-white/80 backdrop-blur-sm rounded-none sm:rounded-2xl lg:rounded-3xl shadow-2xl border-0 sm:border border-white/20 overflow-hidden flex flex-col lg:flex-row">
        {/* Left Side - Chat Interface */}
        <div className="flex-1 flex flex-col order-2 lg:order-1">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 px-3 sm:px-6 py-3 sm:py-4 flex items-center space-x-2 sm:space-x-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Bot className="w-4 h-4 sm:w-6 sm:h-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h1 className="text-lg sm:text-xl font-semibold text-white truncate">
                AI Speaking Assistant
              </h1>
              
            </div>
            <div className="flex items-center space-x-1 sm:space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white/80 text-xs sm:text-sm">Online</span>
            </div>
          </div>

          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-3 sm:p-6 space-y-3 sm:space-y-4 bg-gradient-to-b from-gray-50/50 to-white/50">
            <AnimatePresence>
              {messages.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex flex-col items-center justify-center h-full text-center px-4"
                >
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mb-3 sm:mb-4">
                    <Bot className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                  </div>
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-700 mb-2">
                    Welcome to AI Speaking Assistant
                  </h3>
                  <p className="text-sm sm:text-base text-gray-500 max-w-sm sm:max-w-md">
                    Start a conversation by typing your message below.
                  </p>
                </motion.div>
              ) : (
                messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className={`flex ${
                      message.sender === "user"
                        ? "justify-end"
                        : "justify-start"
                    }`}
                  >
                    <div
                      className={`flex items-start space-x-2 sm:space-x-3 max-w-[85%] sm:max-w-[75%] lg:max-w-[70%] ${
                        message.sender === "user"
                          ? "flex-row-reverse space-x-reverse"
                          : ""
                      }`}
                    >
                      <div
                        className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                          message.sender === "user"
                            ? "bg-gradient-to-br from-blue-500 to-indigo-600"
                            : "bg-gradient-to-br from-purple-500 to-pink-600"
                        }`}
                      >
                        {message.sender === "user" ? (
                          <User className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                        ) : (
                          <Bot className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                        )}
                      </div>
                      <div
                        className={`rounded-xl sm:rounded-2xl px-3 py-2 sm:px-4 sm:py-3 ${
                          message.sender === "user"
                            ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                            : "bg-white border border-gray-200 text-gray-800 shadow-sm"
                        }`}
                      >
                        <p className="text-xs sm:text-sm leading-relaxed">
                          {message.text}
                        </p>
                        <p
                          className={`text-xs mt-1 ${
                            message.sender === "user"
                              ? "text-blue-100"
                              : "text-gray-400"
                          }`}
                        >
                          {message.timestamp.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </AnimatePresence>

            {loading && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="flex items-start space-x-2 sm:space-x-3 max-w-[85%] sm:max-w-[75%] lg:max-w-[70%]">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center flex-shrink-0">
                    <Bot className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  </div>
                  <div className="bg-white border border-gray-200 rounded-xl sm:rounded-2xl px-3 py-2 sm:px-4 sm:py-3 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin text-blue-500" />
                      <span className="text-xs sm:text-sm text-gray-600">
                        Creating your video response...
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Input Area */}
          <div className="border-t border-gray-200 bg-white/70 backdrop-blur-sm p-3 sm:p-4">
            <form onSubmit={handleSubmit} className="flex items-end space-x-2 sm:space-x-3">
              <div className="flex-1 relative">
                <textarea
                  className="w-full border-2 border-gray-200 rounded-xl sm:rounded-2xl px-3 py-2 sm:px-4 sm:py-3 pr-8 sm:pr-12 resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200/50 transition-all duration-200 placeholder-gray-400 bg-white/80 backdrop-blur-sm text-sm sm:text-base"
                  rows={1}
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your message or use the microphone..."
                  disabled={loading}
                  style={{ minHeight: "40px", maxHeight: "120px" }}
                />
                <div className="absolute bottom-1 sm:bottom-2 right-2 sm:right-3 text-gray-400 text-xs mb-4">
                  {text.length}/500
                </div>
              </div>

              {/* Microphone Button */}
              <motion.button
                type="button"
                onClick={isRecording ? stopRecording : startRecording}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`p-2 sm:p-3 rounded-xl sm:rounded-2xl font-medium transition-all duration-200 flex items-center justify-center mb-3 ${
                  isRecording
                    ? "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-xl"
                    : isTranscribing
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl"
                }`}
                disabled={loading || isTranscribing}
              >
                {isTranscribing ? (
                  <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                ) : isRecording ? (
                  <MicOff className="w-4 h-4 sm:w-5 sm:h-5" />
                ) : (
                  <Mic className="w-4 h-4 sm:w-5 sm:h-5" />
                )}
              </motion.button>

              {/* Send Button */}
              <motion.button
                type="submit"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`p-2 sm:p-3 rounded-xl sm:rounded-2xl font-medium transition-all duration-200 flex items-center justify-center mb-3 ${
                  loading || text.trim() === ""
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl"
                }`}
                disabled={loading || text.trim() === ""}
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                ) : (
                  <Send className="w-4 h-4 sm:w-5 sm:h-5" />
                )}
              </motion.button>

              
            </form>
          </div>
        </div>

        {/* Right Side - Avatar */}
        <div className="w-full lg:w-96 h-64 lg:h-auto bg-gradient-to-br from-indigo-50 to-purple-50 border-t lg:border-t-0 lg:border-l border-gray-200/50 flex flex-col relative overflow-hidden order-1 lg:order-2">
          {/* Avatar Container - Takes full height */}
          <div className="flex-1 relative">
            {videoUrl ? (
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="w-full h-full relative"
              >
                <video
                  src={videoUrl}
                  controls
                  autoPlay
                  className="w-full h-full object-cover"
                />
                {/* Status indicator overlay */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute top-4 right-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2"
                >
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-white text-sm font-medium">Speaking</span>
                </motion.div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="w-full h-full bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 flex flex-col items-center justify-center relative"
              >
                {/* Large Bot Icon */}
                <Bot className="w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 text-white/90 mb-4 lg:mb-8" />

                {/* Avatar Info */}
                <div className="text-center text-white px-4">
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-1 lg:mb-2">AI Assistant</h3>

                  <div className="flex items-center justify-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 lg:px-4 lg:py-2">
                    <div className="w-2 h-2 lg:w-3 lg:h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-white font-medium text-sm lg:text-base">Online</span>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-4 left-4 lg:top-8 lg:left-8 w-8 h-8 lg:w-16 lg:h-16 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute bottom-6 right-6 lg:bottom-12 lg:right-12 w-12 h-12 lg:w-24 lg:h-24 bg-white/5 rounded-full blur-2xl"></div>
                <div className="absolute top-1/3 right-4 lg:right-8 w-4 h-4 lg:w-8 lg:h-8 bg-white/20 rounded-full blur-sm"></div>
              </motion.div>
            )}
          </div>

          {/* Loading Overlay */}
          {loading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="absolute inset-0 bg-black/40 backdrop-blur-sm flex flex-col items-center justify-center"
            >
              <div className="bg-white/90 backdrop-blur-sm rounded-xl lg:rounded-2xl p-4 sm:p-6 lg:p-8 text-center shadow-2xl mx-4 max-w-sm">
                <Loader2 className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 animate-spin text-indigo-600 mx-auto mb-3 lg:mb-4" />
                <p className="text-sm sm:text-base text-gray-600 mb-4 lg:mb-6">Please wait while I process your message...</p>

                {/* Progress bar */}
                <div className="w-48 sm:w-56 lg:w-64 h-1.5 lg:h-2 bg-gray-200 rounded-full overflow-hidden mx-auto">
                  <motion.div
                    className="h-full bg-gradient-to-r from-blue-500 to-indigo-600"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
