# Changelog

[npm history][1]

[1]: https://www.npmjs.com/package/@google-cloud/common?activeTab=versions

## [5.0.2](https://github.com/googleapis/nodejs-common/compare/v5.0.1...v5.0.2) (2024-04-30)


### Bug Fixes

* Replace ent with html-entities ([7ec0a32](https://github.com/googleapis/nodejs-common/commit/7ec0a324d8e41e5c74ab8ac08b94fca9514ae893))

## [5.0.1](https://github.com/googleapis/nodejs-common/compare/v5.0.0...v5.0.1) (2023-10-26)


### Bug Fixes

* **deps:** Update dependency retry-request to v7 ([#794](https://github.com/googleapis/nodejs-common/issues/794)) ([beb2e1c](https://github.com/googleapis/nodejs-common/commit/beb2e1cefab2c6012beb4fdc92f239f4195d6a04))

## [5.0.0](https://github.com/googleapis/nodejs-common/compare/v4.0.3...v5.0.0) (2023-08-09)


### ⚠ BREAKING CHANGES

* upgrade to Node 14 ([#782](https://github.com/googleapis/nodejs-common/issues/782))

### Miscellaneous Chores

* Upgrade to Node 14 ([#782](https://github.com/googleapis/nodejs-common/issues/782)) ([9651316](https://github.com/googleapis/nodejs-common/commit/96513160dd3d9deabc4e4f30509386a2171bb50e))

## [4.0.3](https://github.com/googleapis/nodejs-common/compare/v4.0.2...v4.0.3) (2022-08-23)


### Bug Fixes

* remove pip install statements ([#1546](https://github.com/googleapis/nodejs-common/issues/1546)) ([#758](https://github.com/googleapis/nodejs-common/issues/758)) ([e619bf9](https://github.com/googleapis/nodejs-common/commit/e619bf98fa965b9aa32bde9a5ae399acac4e0708))

## [4.0.2](https://github.com/googleapis/nodejs-common/compare/v4.0.1...v4.0.2) (2022-06-21)


### Bug Fixes

* Requests Respect `config.projectIdRequired` === `false` ([#753](https://github.com/googleapis/nodejs-common/issues/753)) ([41934ef](https://github.com/googleapis/nodejs-common/commit/41934efbbc3e2b62f97142d46ae594d243ed31b8))

## [4.0.1](https://github.com/googleapis/nodejs-common/compare/v4.0.0...v4.0.1) (2022-06-13)


### Bug Fixes

* **deps:** update dependency @google-cloud/projectify to v3 ([#754](https://github.com/googleapis/nodejs-common/issues/754)) ([d9a74ec](https://github.com/googleapis/nodejs-common/commit/d9a74ec0cb232cdb44b36f2e4ea11c7435d28099))

## [4.0.0](https://github.com/googleapis/nodejs-common/compare/v3.10.0...v4.0.0) (2022-06-07)


### ⚠ BREAKING CHANGES

* update library to use Node 12 (#749)

### Bug Fixes

* **deps:** update dependency @google-cloud/promisify to v3 ([#746](https://github.com/googleapis/nodejs-common/issues/746)) ([8333d1d](https://github.com/googleapis/nodejs-common/commit/8333d1d4dc02a623b7dd1034e019170cfde8db3a))
* **deps:** update dependency retry-request to v5 ([#748](https://github.com/googleapis/nodejs-common/issues/748)) ([e07fe0f](https://github.com/googleapis/nodejs-common/commit/e07fe0f711b2ba7e367879abe995dddc7a5fefa7))
* **deps:** update dependency teeny-request to v8 ([#751](https://github.com/googleapis/nodejs-common/issues/751)) ([4317850](https://github.com/googleapis/nodejs-common/commit/4317850e1c1b6510681b567f9f2961f255134450))


### Build System

* update library to use Node 12 ([#749](https://github.com/googleapis/nodejs-common/issues/749)) ([3d5acce](https://github.com/googleapis/nodejs-common/commit/3d5accebfd198f33fc93a7dc7e8cd2d1cb7064f5))

## [3.10.0](https://github.com/googleapis/nodejs-common/compare/v3.9.0...v3.10.0) (2022-02-22)


### Features

* Support `AuthClient` for `authClient` ([#732](https://github.com/googleapis/nodejs-common/issues/732)) ([d4c39f3](https://github.com/googleapis/nodejs-common/commit/d4c39f385ea715c79f9bae1853748700dc96c152))

## [3.9.0](https://github.com/googleapis/nodejs-common/compare/v3.8.1...v3.9.0) (2022-01-19)


### Features

* add optional projectId override in service object ([#722](https://github.com/googleapis/nodejs-common/issues/722)) ([4908d85](https://github.com/googleapis/nodejs-common/commit/4908d859c55a3ed40dd82849c7f2658840c920c1))

### [3.8.1](https://www.github.com/googleapis/nodejs-common/compare/v3.8.0...v3.8.1) (2021-11-03)


### Bug Fixes

* disable retrying no response if autoRetry is set to false ([#707](https://www.github.com/googleapis/nodejs-common/issues/707)) ([150b2b0](https://www.github.com/googleapis/nodejs-common/commit/150b2b02d80dd9a3218f21d32fea2198845b8863))

## [3.8.0](https://www.github.com/googleapis/nodejs-common/compare/v3.7.3...v3.8.0) (2021-11-01)


### Features

* Add retry support for 504 (Gateway Timeout) ([#704](https://www.github.com/googleapis/nodejs-common/issues/704)) ([5a3a96f](https://www.github.com/googleapis/nodejs-common/commit/5a3a96f5aa607e53d71e7ed9af5f26431af45679))


### Bug Fixes

* allow authentication with custom endpoint ([#698](https://www.github.com/googleapis/nodejs-common/issues/698)) ([4c0f7ec](https://www.github.com/googleapis/nodejs-common/commit/4c0f7ec0c6ebedc34eaaab79fc053e7e889e243d))
* update table.create to not reset id ([#700](https://www.github.com/googleapis/nodejs-common/issues/700)) ([b1ceb53](https://www.github.com/googleapis/nodejs-common/commit/b1ceb532ac7bf4852f430c83af5a71618ed42f54))

### [3.7.4](https://www.github.com/googleapis/nodejs-common/compare/v3.7.3...v3.7.4) (2021-10-06)


### Bug Fixes

* allow authentication with custom endpoint ([#698](https://www.github.com/googleapis/nodejs-common/issues/698)) ([4c0f7ec](https://www.github.com/googleapis/nodejs-common/commit/4c0f7ec0c6ebedc34eaaab79fc053e7e889e243d))

### [3.7.3](https://www.github.com/googleapis/nodejs-common/compare/v3.7.2...v3.7.3) (2021-10-06)


### Bug Fixes

* updated instance id to match returned metadata id ([#696](https://www.github.com/googleapis/nodejs-common/issues/696)) ([f15b02d](https://www.github.com/googleapis/nodejs-common/commit/f15b02dca69dd487df241b36a50e6a06efbfb697))

### [3.7.2](https://www.github.com/googleapis/nodejs-common/compare/v3.7.1...v3.7.2) (2021-09-03)


### Bug Fixes

* **build:** migrate to main branch ([#689](https://www.github.com/googleapis/nodejs-common/issues/689)) ([0f4bef8](https://www.github.com/googleapis/nodejs-common/commit/0f4bef834e68032396c4b7e5dbdb71f44e8f3f95))

### [3.7.1](https://www.github.com/googleapis/nodejs-common/compare/v3.7.0...v3.7.1) (2021-08-06)


### Bug Fixes

* correctly locate projectId from auth library ([#663](https://www.github.com/googleapis/nodejs-common/issues/663)) ([72e3168](https://www.github.com/googleapis/nodejs-common/commit/72e3168bcac1d177360257197293a566c5fb63d3))

## [3.7.0](https://www.github.com/googleapis/nodejs-common/compare/v3.6.0...v3.7.0) (2021-07-09)


### Features

* Customize retry implementation ([#680](https://www.github.com/googleapis/nodejs-common/issues/680)) ([b529998](https://www.github.com/googleapis/nodejs-common/commit/b529998cdd1c8a7f159846f65651e2118bae5d9e))

## [3.6.0](https://www.github.com/googleapis/nodejs-common/compare/v3.5.0...v3.6.0) (2021-02-17)


### Features

* **deps:** google-auth-library with workload identity federation ([#649](https://www.github.com/googleapis/nodejs-common/issues/649)) ([31945ac](https://www.github.com/googleapis/nodejs-common/commit/31945accc3fbfed513ab54c63038685a219429f4)), closes [#648](https://www.github.com/googleapis/nodejs-common/issues/648)

## [3.5.0](https://www.github.com/googleapis/nodejs-common/compare/v3.4.1...v3.5.0) (2020-11-11)


### Features

* add ignoreNotFound to service-object#delete ([#634](https://www.github.com/googleapis/nodejs-common/issues/634)) ([3248e27](https://www.github.com/googleapis/nodejs-common/commit/3248e27c3776d705720f69c2dcf48d51f2cc4e5c))

### [3.4.1](https://www.github.com/googleapis/nodejs-common/compare/v3.4.0...v3.4.1) (2020-10-06)


### Bug Fixes

* **deps:** upgrade google-auth-library ([#630](https://www.github.com/googleapis/nodejs-common/issues/630)) ([611d16b](https://www.github.com/googleapis/nodejs-common/commit/611d16ba465b53d5eeb3ad59d37a2501a77c3b87))

## [3.4.0](https://www.github.com/googleapis/nodejs-common/compare/v3.3.3...v3.4.0) (2020-09-12)


### Features

* support user-provided auth clients ([#623](https://www.github.com/googleapis/nodejs-common/issues/623)) ([a053e40](https://www.github.com/googleapis/nodejs-common/commit/a053e40a91e647a47dc44ba47cfb86775904556d))

### [3.3.3](https://www.github.com/googleapis/nodejs-common/compare/v3.3.2...v3.3.3) (2020-08-28)


### Bug Fixes

* move system and samples test from Node 10 to Node 12 ([#619](https://www.github.com/googleapis/nodejs-common/issues/619)) ([8dee48f](https://www.github.com/googleapis/nodejs-common/commit/8dee48f78b5f8df25ec97bd0dc2be731481bba35))

### [3.3.2](https://www.github.com/googleapis/nodejs-common/compare/v3.3.1...v3.3.2) (2020-07-09)


### Bug Fixes

* typeo in nodejs .gitattribute ([#597](https://www.github.com/googleapis/nodejs-common/issues/597)) ([50269c2](https://www.github.com/googleapis/nodejs-common/commit/50269c23b0e22f4affbaa20d1c2c2947d824dc9e))

### [3.3.1](https://www.github.com/googleapis/nodejs-common/compare/v3.3.0...v3.3.1) (2020-07-06)


### Bug Fixes

* allow non-JSON body ([#587](https://www.github.com/googleapis/nodejs-common/issues/587)) ([dbaad17](https://www.github.com/googleapis/nodejs-common/commit/dbaad170a2e3a6785568523086f15d88fb34eaca))

## [3.3.0](https://www.github.com/googleapis/nodejs-common/compare/v3.2.0...v3.3.0) (2020-06-29)


### Features

* add ServiceObject#getRequestInterceptors() ([#591](https://www.github.com/googleapis/nodejs-common/issues/591)) ([ade7e50](https://www.github.com/googleapis/nodejs-common/commit/ade7e50558e87b514adf48940f6e4413b2160b37))

## [3.2.0](https://www.github.com/googleapis/nodejs-common/compare/v3.1.1...v3.2.0) (2020-06-23)


### Features

* expose method to get request interceptors ([#589](https://www.github.com/googleapis/nodejs-common/issues/589)) ([d01507f](https://www.github.com/googleapis/nodejs-common/commit/d01507f3fae41ffdffa2056bfb1aa40d97ffb653))


### Bug Fixes

* **deps:** update dependency teeny-request to v7 ([#581](https://www.github.com/googleapis/nodejs-common/issues/581)) ([8d12007](https://www.github.com/googleapis/nodejs-common/commit/8d12007c5b43b8b6a679b0a925c05cb665d850b7))

### [3.1.1](https://www.github.com/googleapis/nodejs-common/compare/v3.1.0...v3.1.1) (2020-05-29)


### Bug Fixes

* allow users to set interceptors ([#579](https://www.github.com/googleapis/nodejs-common/issues/579)) ([75f0d8a](https://www.github.com/googleapis/nodejs-common/commit/75f0d8ad92702a893b614d60f36aea09fe20bb30))

## [3.1.0](https://www.github.com/googleapis/nodejs-common/compare/v3.0.0...v3.1.0) (2020-05-08)


### Features

* **service:** add optional provided user agent ([#566](https://www.github.com/googleapis/nodejs-common/issues/566)) ([a0b814e](https://www.github.com/googleapis/nodejs-common/commit/a0b814ead58c8b255de2da8044c81d1be7b3825d))


### Bug Fixes

* **deps:** update dependency google-auth-library to v6 ([#556](https://www.github.com/googleapis/nodejs-common/issues/556)) ([03a8a54](https://www.github.com/googleapis/nodejs-common/commit/03a8a542ac329f63776388346d1f74732b0ab984))
* apache license URL ([#468](https://www.github.com/googleapis/nodejs-common/issues/468)) ([#564](https://www.github.com/googleapis/nodejs-common/issues/564)) ([1adc855](https://www.github.com/googleapis/nodejs-common/commit/1adc855c17091b67e7f8821e0286ce7256023040))
* retry error code 408 ([#578](https://www.github.com/googleapis/nodejs-common/issues/578)) ([0d3239d](https://www.github.com/googleapis/nodejs-common/commit/0d3239d9eeb03bb9cb49e1107d3d6d4864ffe341))

## [3.0.0](https://www.github.com/googleapis/nodejs-common/compare/v2.4.0...v3.0.0) (2020-03-26)


### ⚠ BREAKING CHANGES

* drop support for node.js 8 (#554)
* remove support for custom promises (#541)

### Features

* add progress events ([#540](https://www.github.com/googleapis/nodejs-common/issues/540)) ([1834059](https://www.github.com/googleapis/nodejs-common/commit/18340596ecb61018e5427371b9b5a120753ec003))


### Bug Fixes

* remove support for custom promises ([#541](https://www.github.com/googleapis/nodejs-common/issues/541)) ([ecf1c16](https://www.github.com/googleapis/nodejs-common/commit/ecf1c167927b609f13dc4fbec1954ff3a2765344))
* **deps:** update dependency @google-cloud/projectify to v2 ([#553](https://www.github.com/googleapis/nodejs-common/issues/553)) ([23030a2](https://www.github.com/googleapis/nodejs-common/commit/23030a25783cd091f4720c25a15416c91e7bd0a0))
* **deps:** update dependency @google-cloud/promisify to v2 ([#552](https://www.github.com/googleapis/nodejs-common/issues/552)) ([63175e0](https://www.github.com/googleapis/nodejs-common/commit/63175e0c4504020466a95e92c2449bdb8ac47546))


### Miscellaneous Chores

* drop support for node.js 8 ([#554](https://www.github.com/googleapis/nodejs-common/issues/554)) ([9f41047](https://www.github.com/googleapis/nodejs-common/commit/9f410477432893f68e57b5eeb31a068a3d8ef52f))

## [2.4.0](https://www.github.com/googleapis/nodejs-common/compare/v2.3.0...v2.4.0) (2020-02-25)


### Features

* if we see EAI_AGAIN error for reason, retry request ([#534](https://www.github.com/googleapis/nodejs-common/issues/534)) ([0debe28](https://www.github.com/googleapis/nodejs-common/commit/0debe28b77d77bf9382e74dc4c11744fb433db6d)), closes [#473](https://www.github.com/googleapis/nodejs-common/issues/473)

## [2.3.0](https://www.github.com/googleapis/nodejs-common/compare/v2.2.6...v2.3.0) (2020-01-23)


### Features

* allow poll interval to be configured ([#520](https://www.github.com/googleapis/nodejs-common/issues/520)) ([abfbd18](https://www.github.com/googleapis/nodejs-common/commit/abfbd189b5769c8dccd482e3c7369711c27b9895))

### [2.2.6](https://www.github.com/googleapis/nodejs-common/compare/v2.2.5...v2.2.6) (2020-01-17)


### Bug Fixes

* **deps:** update dependency teeny-request to v6 ([#517](https://www.github.com/googleapis/nodejs-common/issues/517)) ([f190f7c](https://www.github.com/googleapis/nodejs-common/commit/f190f7ce054e6e25dedfa6b71eb161b0fef52335))

### [2.2.5](https://www.github.com/googleapis/nodejs-common/compare/v2.2.4...v2.2.5) (2019-12-05)


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([e78bd5f](https://www.github.com/googleapis/nodejs-common/commit/e78bd5fafb9e864872e5def4ec71bf5d04acdb19))

### [2.2.4](https://www.github.com/googleapis/nodejs-common/compare/v2.2.3...v2.2.4) (2019-11-13)


### Bug Fixes

* **docs:** add jsdoc-region-tag plugin ([#500](https://www.github.com/googleapis/nodejs-common/issues/500)) ([e1111d0](https://www.github.com/googleapis/nodejs-common/commit/e1111d0ef643c49d6c8f460be89f100d8826aca1))

### [2.2.3](https://www.github.com/googleapis/nodejs-common/compare/v2.2.2...v2.2.3) (2019-10-18)


### Bug Fixes

* **deps:** force newer auth library with various fixes ([#495](https://www.github.com/googleapis/nodejs-common/issues/495)) ([a9c6e43](https://www.github.com/googleapis/nodejs-common/commit/a9c6e4384ddd386955fde4eb52561fc47840e8f3))

### [2.2.2](https://www.github.com/googleapis/nodejs-common/compare/v2.2.1...v2.2.2) (2019-09-18)


### Bug Fixes

* capture message in ApiError.stack ([#466](https://www.github.com/googleapis/nodejs-common/issues/466)) ([811c7cd](https://www.github.com/googleapis/nodejs-common/commit/811c7cd))

### [2.2.1](https://www.github.com/googleapis/nodejs-common/compare/v2.2.0...v2.2.1) (2019-09-12)


### Bug Fixes

* do not block API 401 errors when auth was provided ([#482](https://www.github.com/googleapis/nodejs-common/issues/482)) ([1b617e6](https://www.github.com/googleapis/nodejs-common/commit/1b617e6))

## [2.2.0](https://www.github.com/googleapis/nodejs-common/compare/v2.1.2...v2.2.0) (2019-09-11)


### Bug Fixes

* **docs:** remove anchor from reference doc link ([#474](https://www.github.com/googleapis/nodejs-common/issues/474)) ([81ebc1f](https://www.github.com/googleapis/nodejs-common/commit/81ebc1f))


### Features

* return more helpful error when authentication fails ([#480](https://www.github.com/googleapis/nodejs-common/issues/480)) ([98d2b7f](https://www.github.com/googleapis/nodejs-common/commit/98d2b7f))

### [2.1.2](https://www.github.com/googleapis/nodejs-common/compare/v2.1.1...v2.1.2) (2019-08-14)


### Bug Fixes

* **types:** correct internal timeout field type ([#470](https://www.github.com/googleapis/nodejs-common/issues/470)) ([e7dd206](https://www.github.com/googleapis/nodejs-common/commit/e7dd206))
* upgrade to version of teeny-request with looser types for method ([#472](https://www.github.com/googleapis/nodejs-common/issues/472)) ([143774c](https://www.github.com/googleapis/nodejs-common/commit/143774c))

### [2.1.1](https://www.github.com/googleapis/nodejs-common/compare/v2.1.0...v2.1.1) (2019-08-13)


### Bug Fixes

* **deps:** teeny-request@5.2.0 with fixes for http ([#467](https://www.github.com/googleapis/nodejs-common/issues/467)) ([e11d46c](https://www.github.com/googleapis/nodejs-common/commit/e11d46c))

## [2.1.0](https://www.github.com/googleapis/nodejs-common/compare/v2.0.5...v2.1.0) (2019-08-05)


### Bug Fixes

* **deps:** upgrade to the latest version of teeny-request ([#448](https://www.github.com/googleapis/nodejs-common/issues/448)) ([bb76f07](https://www.github.com/googleapis/nodejs-common/commit/bb76f07))


### Features

* adds timeout to options and use it in reqOpts ([#455](https://www.github.com/googleapis/nodejs-common/issues/455)) ([90a6097](https://www.github.com/googleapis/nodejs-common/commit/90a6097))

### [2.0.5](https://www.github.com/googleapis/nodejs-common/compare/v2.0.4...v2.0.5) (2019-07-29)


### Bug Fixes

* **deps:** update dependency google-auth-library to v5 ([#453](https://www.github.com/googleapis/nodejs-common/issues/453)) ([755635c](https://www.github.com/googleapis/nodejs-common/commit/755635c))

### [2.0.4](https://www.github.com/googleapis/nodejs-common/compare/v2.0.3...v2.0.4) (2019-07-11)


### Bug Fixes

* allow methodConfig to override request method and uri ([#451](https://www.github.com/googleapis/nodejs-common/issues/451)) ([8c2f903](https://www.github.com/googleapis/nodejs-common/commit/8c2f903))

### [2.0.3](https://www.github.com/googleapis/nodejs-common/compare/v2.0.2...v2.0.3) (2019-06-26)


### Bug Fixes

* **docs:** link to reference docs section on googleapis.dev ([#443](https://www.github.com/googleapis/nodejs-common/issues/443)) ([11ccb28](https://www.github.com/googleapis/nodejs-common/commit/11ccb28))

### [2.0.2](https://www.github.com/googleapis/nodejs-common/compare/v2.0.1...v2.0.2) (2019-06-14)


### Bug Fixes

* **docs:** move to new client docs URL ([#438](https://www.github.com/googleapis/nodejs-common/issues/438)) ([1a52715](https://www.github.com/googleapis/nodejs-common/commit/1a52715))

### [2.0.1](https://www.github.com/googleapis/nodejs-common/compare/v2.0.0...v2.0.1) (2019-06-11)


### Bug Fixes

* **deps:** teeny-request was retrying some requests in error ([#436](https://www.github.com/googleapis/nodejs-common/issues/436)) ([00a8ba2](https://www.github.com/googleapis/nodejs-common/commit/00a8ba2))

## [2.0.0](https://www.github.com/googleapis/nodejs-common/compare/v1.0.0...v2.0.0) (2019-05-23)


### ⚠ BREAKING CHANGES

* This adds the apiEndpoint property as a required field for the ServiceConfig, and makes it a public property on the Service class. This is being added to broadly support apiEndpoint overrides.

### Features

* add apiEndpoint property to service config ([#427](https://www.github.com/googleapis/nodejs-common/issues/427)) ([c063df8](https://www.github.com/googleapis/nodejs-common/commit/c063df8)), closes [googleapis/nodejs-bigquery#455](https://www.github.com/googleapis/nodejs-common/issues/455)

## [1.0.0](https://www.github.com/googleapis/nodejs-common/compare/v0.32.1...v1.0.0) (2019-05-09)


### Bug Fixes

* **deps:** update dependency @google-cloud/projectify to v1 ([#414](https://www.github.com/googleapis/nodejs-common/issues/414)) ([6cdc2fe](https://www.github.com/googleapis/nodejs-common/commit/6cdc2fe))
* **deps:** update dependency @google-cloud/promisify to v1 ([#415](https://www.github.com/googleapis/nodejs-common/issues/415)) ([00c422a](https://www.github.com/googleapis/nodejs-common/commit/00c422a))
* **deps:** update dependency google-auth-library to v4 ([#422](https://www.github.com/googleapis/nodejs-common/issues/422)) ([e0a94af](https://www.github.com/googleapis/nodejs-common/commit/e0a94af))


### Build System

* upgrade engines field to >=8.10.0 ([#412](https://www.github.com/googleapis/nodejs-common/issues/412)) ([4349d68](https://www.github.com/googleapis/nodejs-common/commit/4349d68))


### Miscellaneous Chores

* **deps:** update dependency gts to v1 ([#407](https://www.github.com/googleapis/nodejs-common/issues/407)) ([8e73d8c](https://www.github.com/googleapis/nodejs-common/commit/8e73d8c))


### BREAKING CHANGES

* **deps:** this will ship async/await in the generated code
* upgrade engines field to >=8.10.0 (#412)

## v0.32.1

04-08-2019 10:53 PDT

### Dependencies

- fix: teeny-request should be dependency
- fix(deps): update dependency arrify to v2 ([#404](https://github.com/googleapis/nodejs-common/pull/404))
- chore: unpin @types/node ([#402](https://github.com/googleapis/nodejs-common/pull/402))

## v0.32.0

04-02-2019 15:11 PDT

**BREAKING CHANGE**: This PR removes the ability to configure a custom implementation of the Request module. This was necessary when we were migrating from request to teeny-request, but that migration is now complete. All interfaces at accepted a custom implementation of request will no longer accept one. teeny-request is now just included in the box.

## Bug Fixes

- fix: @types/node@11.13.0 breaks paginate/promisifyAll ([#397](https://github.com/googleapis/nodejs-common/pull/397))
- fix(ts): do not ship @types/duplexify ([#393](https://github.com/googleapis/nodejs-common/pull/393))
- fix(deps): bump the min version required for all deps

### Implementation Changes

- refactor: remove configurable request ([#394](https://github.com/googleapis/nodejs-common/pull/394))

### Dependencies

- chore(deps): update dependency @types/tmp to v0.1.0
- chore(deps): update dependency typescript to ~3.4.0
- chore(deps): update dependency tmp to v0.1.0 ([#390](https://github.com/googleapis/nodejs-common/pull/390))

### Internal / Testing Changes

- build: use per-repo publish token ([#384](https://github.com/googleapis/nodejs-common/pull/384))

## v0.31.1

03-13-2019 16:25 PDT

### Bug Fixes
- fix(autoCreate): leave user options intact ([#378](https://github.com/googleapis/nodejs-common/pull/378))

### Dependencies
- fix(deps): update dependency @google-cloud/promisify to ^0.4.0 ([#374](https://github.com/googleapis/nodejs-common/pull/374))

### Documentation
- docs: update links in contrib guide ([#375](https://github.com/googleapis/nodejs-common/pull/375))
- docs: update contributing path in README ([#369](https://github.com/googleapis/nodejs-common/pull/369))
- docs: move CONTRIBUTING.md to root ([#368](https://github.com/googleapis/nodejs-common/pull/368))

### Internal / Testing Changes
- build: Add docuploader credentials to node publish jobs ([#381](https://github.com/googleapis/nodejs-common/pull/381))
- build: use node10 to run samples-test, system-test etc ([#380](https://github.com/googleapis/nodejs-common/pull/380))
- build: update release configuration
- chore(deps): update dependency mocha to v6
- test: do not depend on request ([#376](https://github.com/googleapis/nodejs-common/pull/376))
- build: use linkinator for docs test ([#372](https://github.com/googleapis/nodejs-common/pull/372))
- chore(deps): update dependency @types/tmp to v0.0.34 ([#373](https://github.com/googleapis/nodejs-common/pull/373))
- build: create docs test npm scripts ([#371](https://github.com/googleapis/nodejs-common/pull/371))
- build: test using @grpc/grpc-js in CI ([#370](https://github.com/googleapis/nodejs-common/pull/370))
- refactor: change error message format to multiline ([#362](https://github.com/googleapis/nodejs-common/pull/362))

## v0.31.0

02-05-2019 16:37 PST
  
### New Features

- fix: remove timeout rule from streaming uploads ([#365](https://github.com/googleapis/nodejs-common/pull/365))

### Dependencies

- deps: update typescript to v3.3.0 ([#358](https://github.com/googleapis/nodejs-common/pull/358))

### Documentation

- docs: add lint/fix example to contributing guide ([#364](https://github.com/googleapis/nodejs-common/pull/364))

## v0.30.2

01-25-2019 12:06 PST

### New Features
- fix: clone default request configuration object ([#356](https://github.com/googleapis/nodejs-common/pull/356))

## v0.30.1

01-25-2019 11:06 PST

### New Features

- fix: favor user options over defaults ([#353](https://github.com/googleapis/nodejs-common/pull/353))

### Documentation

- build: ignore googleapis.com in doc link check ([#351](https://github.com/googleapis/nodejs-common/pull/351))

### Internal / Testing Changes

- add tests ([#352](https://github.com/googleapis/nodejs-common/pull/352))

## v0.30.0

01-23-2019 06:21 PST

### New Features

- fix: inherit requestModule from parent ([#344](https://github.com/googleapis/nodejs-common/pull/344))
- feat: allow options to ServiceObject methods ([#349](https://github.com/googleapis/nodejs-common/pull/349))

### Dependencies

- chore(deps): update dependency google-auth-library to v3.0.0 ([#348](https://github.com/googleapis/nodejs-common/pull/348))
- chore(deps): update dependency @types/sinon to v7.0.3 ([#346](https://github.com/googleapis/nodejs-common/pull/346))
- chore(deps): update dependency @types/sinon to v7.0.2 ([#343](https://github.com/googleapis/nodejs-common/pull/343))

### Internal / Testing Changes

- build: check for 404s in the docs ([#347](https://github.com/googleapis/nodejs-common/pull/347))

## v0.29.1

12-19-2018 20:57 PST

### Bug fixes
- fix: bind to this instead of true ([#341](https://github.com/googleapis/nodejs-common/pull/341))

## v0.29.0

12-19-2018 13:11 PST

- fix: use request_ for service-object ([#337](https://github.com/googleapis/nodejs-common/pull/337))

## v0.28.0

12-13-2018 14:34 PST

**This release has breaking changes**.  The signature of the protected `request` method on `ServiceObject` has been changed.  The method now resolves with an array of `[Body, Response]`, making it consistent with all other promisified methods.  This change was made to fix several breaking changes that occurred in the `0.18.0` release.

### New Features
- feat: allow passing GoogleAuth client to Service ([#314](https://github.com/googleapis/nodejs-common/pull/314))
- feat: add maybeOptionsOrCallback util method ([#315](https://github.com/googleapis/nodejs-common/pull/315))

### Bug Fixes
- fix: revert async behavior of request ([#331](https://github.com/googleapis/nodejs-common/pull/331))

### Documentation
- docs: update readme badges ([#316](https://github.com/googleapis/nodejs-common/pull/316))

### Internal / Testing Changes
- chore(deps): update dependency @types/sinon to v7 ([#332](https://github.com/googleapis/nodejs-common/pull/332))
- chore(build): inject yoshi automation key ([#330](https://github.com/googleapis/nodejs-common/pull/330))
- chore: update nyc and eslint configs ([#329](https://github.com/googleapis/nodejs-common/pull/329))
- chore: fix publish.sh permission +x ([#327](https://github.com/googleapis/nodejs-common/pull/327))
- fix(build): fix Kokoro release script ([#326](https://github.com/googleapis/nodejs-common/pull/326))
- build: add Kokoro configs for autorelease ([#325](https://github.com/googleapis/nodejs-common/pull/325))
- chore: always nyc report before calling codecov ([#322](https://github.com/googleapis/nodejs-common/pull/322))
- chore: nyc ignore build/test by default ([#321](https://github.com/googleapis/nodejs-common/pull/321))
- chore(build): update the prettier config ([#319](https://github.com/googleapis/nodejs-common/pull/319))
- chore: update license file ([#318](https://github.com/googleapis/nodejs-common/pull/318))
- fix(build): fix system key decryption ([#313](https://github.com/googleapis/nodejs-common/pull/313))
- chore(deps): update dependency @types/sinon to v5.0.7 ([#308](https://github.com/googleapis/nodejs-common/pull/308))
- chore(deps): update dependency typescript to ~3.2.0 ([#312](https://github.com/googleapis/nodejs-common/pull/312))

## v0.27.0

11-26-2018 12:26 PST

**BREAKING CHANGE**: The `ServiceObject` class now has stricter TypeScript types for property names.  This will have no runtime impact, but may cause TypeScript compilation errors until the issues are addressed.

### Fixes
- fix: improve types for service object ([#310](https://github.com/googleapis/nodejs-common/pull/310))
- refactor: drop through2, mv, and a few others ([#306](https://github.com/googleapis/nodejs-common/pull/306))

### Internal / Testing Changes
- chore: add a synth.metadata
- fix: Pin @types/sinon to last compatible version ([#307](https://github.com/googleapis/nodejs-common/pull/307))

## v0.26.2

This patch release also brings in a patch dependency update of @google-cloud/projectify which contains a fix for OOM issue.

### Implementation Changes
- ts: genericize CreateOptions in ServiceObject ([#275](https://github.com/googleapis/nodejs-common/pull/275))

### Dependencies
- chore(deps): upgrade @google-cloud/projectify to v0.3.2 ([#301](https://github.com/googleapis/nodejs-common/pull/301))
- chore(deps): update dependency gts to ^0.9.0 ([#300](https://github.com/googleapis/nodejs-common/pull/300))
- chore(deps): update dependency @google-cloud/nodejs-repo-tools to v3 ([#298](https://github.com/googleapis/nodejs-common/pull/298))
- fix(deps): update dependency through2 to v3 ([#295](https://github.com/googleapis/nodejs-common/pull/295))

### Internal / Testing Changes
- chore: update eslintignore config ([#299](https://github.com/googleapis/nodejs-common/pull/299))
- chore: drop contributors from multiple places ([#297](https://github.com/googleapis/nodejs-common/pull/297))
- chore: use latest npm on Windows ([#296](https://github.com/googleapis/nodejs-common/pull/296))
- chore: update CircleCI config ([#294](https://github.com/googleapis/nodejs-common/pull/294))

## v0.26.1

### Dependencies
- chore(deps): upgrade @google-cloud/projectify to ^0.3.1 ([#289](https://github.com/googleapis/nodejs-common/pull/289))

### Internal / Testing Changes
- chore: include build in eslintignore ([#288](https://github.com/googleapis/nodejs-common/pull/288))
- chore: update issue templates ([#284](https://github.com/googleapis/nodejs-common/pull/284))
- chore: remove old issue template ([#282](https://github.com/googleapis/nodejs-common/pull/282))
- build: run tests on node11 ([#280](https://github.com/googleapis/nodejs-common/pull/280))

## v0.26.0

### Implementation Changes
- fix(typescript): Make ResponseCallback match subtype ([#271](https://github.com/googleapis/nodejs-common/pull/271))
- fix: Do not retry streaming POST requests. ([#268](https://github.com/googleapis/nodejs-common/pull/268))
- Don't publish sourcemaps ([#256](https://github.com/googleapis/nodejs-common/pull/256))

### Dependencies
- chore: Remove 'is' dependency ([#270](https://github.com/googleapis/nodejs-common/pull/270))
- chore(deps): update dependency sinon to v7 ([#267](https://github.com/googleapis/nodejs-common/pull/267))
- chore(deps): update dependency typescript to ~3.1.0 ([#259](https://github.com/googleapis/nodejs-common/pull/259))

### Internal / Testing Changes
- chores(build): run codecov on continuous builds ([#276](https://github.com/googleapis/nodejs-common/pull/276))
- chore: update new issue template ([#274](https://github.com/googleapis/nodejs-common/pull/274))
- chore: re-enable codecov ([#266](https://github.com/googleapis/nodejs-common/pull/266))
- test: move install to system tests, and other tsconfig cleanup ([#269](https://github.com/googleapis/nodejs-common/pull/269))
- Update kokoro config ([#264](https://github.com/googleapis/nodejs-common/pull/264))
- docs: Remove appveyor badge from readme ([#262](https://github.com/googleapis/nodejs-common/pull/262))
- Update CI config ([#258](https://github.com/googleapis/nodejs-common/pull/258))
- build: prevent system/sample-test from leaking credentials
- Update the kokoro config ([#254](https://github.com/googleapis/nodejs-common/pull/254))
- test: remove appveyor config ([#253](https://github.com/googleapis/nodejs-common/pull/253))
- Update CI config ([#252](https://github.com/googleapis/nodejs-common/pull/252))

## v0.25.3

### Bug fixes
- fix(types): improve TypeScript types ([#248](https://github.com/googleapis/nodejs-common/pull/248))

## v0.25.2

### Bug fixes
- fix(service): Use getProjectId instead of getDefaultProjectId ([#246](https://github.com/googleapis/nodejs-common/pull/246))

## v0.25.1

### Implementation Changes
- Improve TypeScript types for async operations ([#241](https://github.com/googleapis/nodejs-common/pull/241))
- Enhance typing of ServiceObject.prototype.get ([#239](https://github.com/googleapis/nodejs-common/pull/239))
- Fix TypeScript setMetadata return type ([#240](https://github.com/googleapis/nodejs-common/pull/240))
- Enable no-var in eslint ([#238](https://github.com/googleapis/nodejs-common/pull/238))

## v0.25.0

### Implementation Changes
Some types improvements.
- Improve types for SO.getMetadata, setMetadata ([#235](https://github.com/googleapis/nodejs-common/pull/235))
- Expose the parent property on service-object ([#233](https://github.com/googleapis/nodejs-common/pull/233))

### Internal / Testing Changes
- Update CI config ([#232](https://github.com/googleapis/nodejs-common/pull/232))

## v0.24.0

**BREAKING CHANGES**: This release includes an update to `google-auth-library` [2.0](https://github.com/google/google-auth-library-nodejs/releases/tag/v2.0.0), which has a variety of breaking changes.

### Bug fixes
- fix: set default once (#226)
- fix: export DecorateRequestOptions and BodyResponseCallback (#225)
- fix: fix the types (#221)

### Dependencies
- fix(deps): update dependency google-auth-library to v2 (#224)
- chore(deps): update dependency nyc to v13 (#223)

## v0.23.0

### Fixes
- fix: move repo-tools to dev dependencies (#218)

### Features
- feat: make HTTP dependency configurable (#210)

### Keepin the lights on
- chore: run repo-tools (#219)

## v0.22.0

### Commits

- fix: Remove old code & replace project ID token in multipart arrays. (#215)
- allow ServiceObject`s parent to be an ServiceObject (#212)
- fix: increase timeout for install test (#214)
- chore: remove dead code and packages (#209)
- fix(deps): update dependency pify to v4 (#208)

## v0.21.1

### Bug fixes
- fix: method metadata can be a boolean (#206)

### Build and Test
- test: throw on deprecation (#198)
- chore(deps): update dependency typescript to v3 (#197)
- chore: ignore package-lock.json (#205)

## v0.21.0

**This release has breaking changes**.

#### Node.js support
Versions 4.x and 9.x of node.js are no longer supported.  Please upgrade to node.js 8.x or 10.x.

#### New npm modules
The support for pagination, promisification, and project Id replacement have been moved into their own npm modules.  You can find them at:
- [@google-cloud/projectify](https://github.com/googleapis/nodejs-projectify)
- [@google-cloud/promisify](https://github.com/googleapis/nodejs-promisify)
- [@google-cloud/paginator](https://github.com/googleapis/nodejs-paginator)

These methods have been removed from `@google-cloud/common`.

### Breaking Changes
- fix: drop support for node.js 4.x and 9.x (#190)
- chore: cut out code split into other modules (#194)

### Implementation Changes
- fix: make ServiceObject#id protected to allow subclass access (#200)

### Internal / Testing Changes
- chore(deps): update dependency gts to ^0.8.0 (#192)
- chore: update renovate config (#202)
- refactor: remove circular imports (#201)
- fix: special JSON.stringify for for strictEqual test (#199)
- chore: assert.deelEqual => assert.deepStrictEqual (#196)
- chore: move mocha options to mocha.opts (#195)
- Update config.yml (#191)
